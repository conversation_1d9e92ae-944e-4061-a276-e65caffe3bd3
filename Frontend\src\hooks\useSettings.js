import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  fetchPublicSettings,
  fetchPlatformCommission,
  selectPublicSettings,
  selectPlatformCommission,
  selectSettingsLoading,
  selectSettingsErrors,
  selectSiteName,
  selectSiteLogo,
  selectContactEmail,
  selectContactPhone,
  selectAddress,
  selectSupportLink,
  selectMaintenanceMode,
  selectPlatformCommissionRate,
  selectSellerEarningsRate,
} from '../redux/slices/settingsSlice';
import { IMAGE_BASE_URL } from '../utils/constants';

/**
 * Custom hook for accessing and managing site settings
 * Provides easy access to all site settings with automatic loading
 */
export const useSettings = (options = {}) => {
  const {
    autoFetch = true,
    fetchPublic = true,
    fetchCommission = true,
  } = options;

  const dispatch = useDispatch();

  // Selectors
  const publicSettings = useSelector(selectPublicSettings);
  const platformCommission = useSelector(selectPlatformCommission);
  const loading = useSelector(selectSettingsLoading);
  const errors = useSelector(selectSettingsErrors);

  // Individual setting selectors
  const siteName = useSelector(selectSiteName);
  const siteLogo = useSelector(selectSiteLogo);
  const contactEmail = useSelector(selectContactEmail);
  const contactPhone = useSelector(selectContactPhone);
  const address = useSelector(selectAddress);
  const supportLink = useSelector(selectSupportLink);
  const maintenanceMode = useSelector(selectMaintenanceMode);
  const platformCommissionRate = useSelector(selectPlatformCommissionRate);
  const sellerEarningsRate = useSelector(selectSellerEarningsRate);

  // Auto-fetch settings on mount
  useEffect(() => {
    if (autoFetch) {
      if (fetchPublic) {
        dispatch(fetchPublicSettings());
      }
      if (fetchCommission) {
        dispatch(fetchPlatformCommission());
      }
    }
  }, [dispatch, autoFetch, fetchPublic, fetchCommission]);

  // Refresh functions
  const refreshPublicSettings = () => dispatch(fetchPublicSettings());
  const refreshPlatformCommission = () => dispatch(fetchPlatformCommission());
  const refreshAllSettings = () => {
    dispatch(fetchPublicSettings());
    dispatch(fetchPlatformCommission());
  };

  // Helper functions
  const getSiteLogoUrl = () => {
    if (!siteLogo) return null;
    // Handle both full URLs and relative paths
    if (siteLogo.startsWith('http')) {
      return siteLogo;
    }
    return `${import.meta.env.VITE_API_URL || 'http://localhost:5000'}${siteLogo}`;
  };

  const isLoading = loading.publicSettings || loading.platformCommission;
  const hasErrors = errors.publicSettings || errors.platformCommission;

  return {
    // Settings data
    publicSettings,
    platformCommission,
    
    // Individual settings
    siteName,
    siteLogo,
    contactEmail,
    contactPhone,
    address,
    supportLink,
    maintenanceMode,
    platformCommissionRate,
    sellerEarningsRate,
    
    // Helper functions
    getSiteLogoUrl,
    
    // State
    loading,
    errors,
    isLoading,
    hasErrors,
    
    // Actions
    refreshPublicSettings,
    refreshPlatformCommission,
    refreshAllSettings,
  };
};

/**
 * Hook specifically for platform commission data
 * Useful for payment-related components
 */
export const usePlatformCommission = () => {
  const dispatch = useDispatch();
  const platformCommission = useSelector(selectPlatformCommission);
  const loading = useSelector(state => state.settings.loading.platformCommission);
  const error = useSelector(state => state.settings.errors.platformCommission);

  useEffect(() => {
    dispatch(fetchPlatformCommission());
  }, [dispatch]);

  const refresh = () => dispatch(fetchPlatformCommission());

  return {
    platformCommission: platformCommission.platformCommission,
    sellerEarnings: platformCommission.sellerEarnings,
    loading,
    error,
    refresh,
  };
};

/**
 * Hook specifically for public site settings
 * Useful for layout components like header, footer, etc.
 */
export const usePublicSettings = () => {
  const dispatch = useDispatch();
  const publicSettings = useSelector(selectPublicSettings);
  const loading = useSelector(state => state.settings.loading.publicSettings);
  const error = useSelector(state => state.settings.errors.publicSettings);

  useEffect(() => {
    dispatch(fetchPublicSettings());
  }, [dispatch]);

  const refresh = () => dispatch(fetchPublicSettings());

  const getSiteLogoUrl = () => {
    if (!publicSettings.siteLogo) return null;
    if (publicSettings.siteLogo.startsWith('http')) {
      return publicSettings.siteLogo;
    }
    return `${IMAGE_BASE_URL || 'http://localhost:5000'}${publicSettings.siteLogo}`;
  };

  return {
    ...publicSettings,
    getSiteLogoUrl,
    loading,
    error,
    refresh,
  };
};

export default useSettings;
