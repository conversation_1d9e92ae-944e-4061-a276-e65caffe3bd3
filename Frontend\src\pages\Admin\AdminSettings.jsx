import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectSettings,
  selectLoading,
  selectErrors,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchSettings,
  updateSettings,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import { fetchPublicSettings, fetchPlatformCommission } from "../../redux/slices/settingsSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminSettings.css";

// Icons
import {
  FaSave,
  FaUndo,
  FaCog,
  FaGlobe,
  FaDollarSign,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaExternalLinkAlt,
  FaToggleOn,
  FaToggleOff,
  FaPercentage,
} from "react-icons/fa";

const AdminSettings = () => {
  const dispatch = useDispatch();
  const settings = useSelector(selectSettings);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Form state
  const [formData, setFormData] = useState({
    general: {
      siteName: "",
      siteLogo: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      supportLink: "",
      maintenanceMode: false,
    },
    financial: {
      platformCommissionPercentage: 5,
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);

  // Fetch settings on component mount
  useEffect(() => {
    dispatch(fetchSettings());
  }, [dispatch]);

  // Update form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        general: {
          siteName: settings.general?.siteName || "",
          siteLogo: settings.general?.siteLogo || "",
          contactEmail: settings.general?.contactEmail || "",
          contactPhone: settings.general?.contactPhone || "",
          address: settings.general?.address || "",
          supportLink: settings.general?.supportLink || "",
          maintenanceMode: settings.general?.maintenanceMode || false,
        },
        financial: {
          platformCommissionPercentage: settings.financial?.platformCommissionPercentage || 5,
        },
      });
    }
  }, [settings]);

  // Handle input changes
  const handleInputChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await dispatch(updateSettings(formData)).unwrap();
      showSuccess("Settings updated successfully!");
      setHasChanges(false);

      // Refresh global settings to reflect changes across the app
      dispatch(fetchPublicSettings());
      dispatch(fetchPlatformCommission());

      // Add activity log
      dispatch(
        addActivity({
          id: Date.now(),
          type: "settings_update",
          description: "Platform settings updated",
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );
    } catch (error) {
      showError(error.message || "Failed to update settings");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle logo upload
  const handleLogoUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      showError('Please select an image file');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return;
    }

    setIsUploadingLogo(true);

    try {
      const formData = new FormData();
      formData.append('logo', file);

      const response = await fetch('/api/admin/settings/upload-logo', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('xosportshub_token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }

      const data = await response.json();

      // Update form data with new logo path
      setFormData(prev => ({
        ...prev,
        general: {
          ...prev.general,
          siteLogo: data.data.siteLogo,
        },
      }));

      setHasChanges(true);
      showSuccess('Logo uploaded successfully!');

    } catch (error) {
      showError(error.message || 'Failed to upload logo');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    if (settings) {
      setFormData({
        general: {
          siteName: settings.general?.siteName || "",
          siteLogo: settings.general?.siteLogo || "",
          contactEmail: settings.general?.contactEmail || "",
          contactPhone: settings.general?.contactPhone || "",
          address: settings.general?.address || "",
          supportLink: settings.general?.supportLink || "",
          maintenanceMode: settings.general?.maintenanceMode || false,
        },
        financial: {
          platformCommissionPercentage: settings.financial?.platformCommissionPercentage || 5,
        },
      });
      setHasChanges(false);
    }
  };

  return (
    <AdminLayout>
      <div className="AdminSettings">
        <div className="AdminSettings__header">
          <div className="header-left">
            <h1>
              <FaCog className="header-icon" />
              Platform Settings
            </h1>
            <p>Manage application-wide settings and configurations</p>
          </div>
          <div className="header-right">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleReset}
              disabled={!hasChanges || isSubmitting}
            >
              <FaUndo />
              Reset Changes
            </button>
            <button
              type="submit"
              form="settings-form"
              className="btn btn-primary"
              disabled={!hasChanges || isSubmitting}
            >
              <FaSave />
              {isSubmitting ? "Saving..." : "Save Settings"}
            </button>
          </div>
        </div>

        {loading.settings ? (
          <div className="AdminSettings__loading">
            <div className="loading-spinner"></div>
            <p>Loading settings...</p>
          </div>
        ) : (
          <form id="settings-form" onSubmit={handleSubmit} className="AdminSettings__form">
            {/* General Settings Section */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <FaGlobe className="section-icon" />
                  General Settings
                </h2>
                <p>Basic site information and configuration</p>
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="siteName">
                    <FaGlobe />
                    Site Name
                  </label>
                  <input
                    type="text"
                    id="siteName"
                    value={formData.general.siteName}
                    onChange={(e) => handleInputChange("general", "siteName", e.target.value)}
                    placeholder="Enter site name"
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="siteLogo">
                    <FaGlobe />
                    Site Logo
                  </label>
                  <div className="logo-upload-container">
                    {formData.general.siteLogo && (
                      <div className="current-logo">
                        <img
                          src={`http://localhost:5000${formData.general.siteLogo}`}
                          alt="Current Logo"
                          className="logo-preview"
                        />
                      </div>
                    )}
                    <input
                      type="file"
                      id="siteLogo"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="form-control file-input"
                      disabled={isUploadingLogo}
                    />
                    {isUploadingLogo && (
                      <div className="upload-progress">
                        <div className="loading-spinner"></div>
                        <span>Uploading...</span>
                      </div>
                    )}
                  </div>
                  <small className="form-text">
                    Upload an image file (max 5MB). Supported formats: JPG, PNG, GIF, SVG
                  </small>
                </div>

                <div className="form-group">
                  <label htmlFor="contactEmail">
                    <FaEnvelope />
                    Contact Email
                  </label>
                  <input
                    type="email"
                    id="contactEmail"
                    value={formData.general.contactEmail}
                    onChange={(e) => handleInputChange("general", "contactEmail", e.target.value)}
                    placeholder="Enter contact email"
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="contactPhone">
                    <FaPhone />
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    id="contactPhone"
                    value={formData.general.contactPhone}
                    onChange={(e) => handleInputChange("general", "contactPhone", e.target.value)}
                    placeholder="Enter contact phone"
                    className="form-control"
                  />
                </div>

                <div className="form-group full-width">
                  <label htmlFor="address">
                    <FaMapMarkerAlt />
                    Address
                  </label>
                  <textarea
                    id="address"
                    value={formData.general.address}
                    onChange={(e) => handleInputChange("general", "address", e.target.value)}
                    placeholder="Enter business address"
                    className="form-control"
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="supportLink">
                    <FaExternalLinkAlt />
                    Support Link
                  </label>
                  <input
                    type="url"
                    id="supportLink"
                    value={formData.general.supportLink}
                    onChange={(e) => handleInputChange("general", "supportLink", e.target.value)}
                    placeholder="Enter support/help center URL"
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="maintenanceMode">
                    Maintenance Mode
                  </label>
                  <div className="toggle-container">
                    <button
                      type="button"
                      className={`toggle-btn ${formData.general.maintenanceMode ? "active" : ""}`}
                      onClick={() => handleInputChange("general", "maintenanceMode", !formData.general.maintenanceMode)}
                    >
                      {formData.general.maintenanceMode ? (
                        <FaToggleOn className="toggle-on" />
                      ) : (
                        <FaToggleOff className="toggle-off" />
                      )}
                      <span>{formData.general.maintenanceMode ? "Enabled" : "Disabled"}</span>
                    </button>
                    <small className="form-text">
                      When enabled, the site will show a maintenance message to users
                    </small>
                  </div>
                </div>
              </div>
            </div>

            {/* Financial Settings Section */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <FaDollarSign className="section-icon" />
                  Financial Settings
                </h2>
                <p>Platform commission and financial configuration</p>
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="platformCommissionPercentage">
                    <FaPercentage />
                    Platform Commission (%)
                  </label>
                  <input
                    type="number"
                    id="platformCommissionPercentage"
                    value={formData.financial.platformCommissionPercentage}
                    onChange={(e) => handleInputChange("financial", "platformCommissionPercentage", parseFloat(e.target.value) || 0)}
                    placeholder="Enter commission percentage"
                    className="form-control"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <small className="form-text">
                    Percentage of each sale that goes to the platform (0-100%)
                  </small>
                </div>
              </div>
            </div>
          </form>
        )}

        {errors.settings && (
          <div className="AdminSettings__error">
            <p>Error: {errors.settings}</p>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
