import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import "../../styles/Footer.css";
import logo from "../../assets/images/XOsports-hub-logo.png";
import {
  FaEnvelope,
  FaFacebookF,
} from "react-icons/fa";
import { FaSquareXTwitter } from "react-icons/fa6";
import { AiFillInstagram } from "react-icons/ai";
import { getPublishedCMSPages } from "../../services/cmsService";
import { usePublicSettings } from "../../hooks/useSettings";




const Footer = () => {
  const [cmsPages, setCmsPages] = useState([]);
  const [loading, setLoading] = useState(true);

  // Get site settings
  const { siteName, getSiteLogoUrl, contactEmail } = usePublicSettings();

  // Fetch published CMS pages on component mount
  useEffect(() => {
    const fetchCMSPages = async () => {
      try {
        setLoading(true);
        const response = await getPublishedCMSPages();
        if (response.success && Array.isArray(response.data)) {
          // Filter out any invalid pages and sort by title
          const validPages = response.data
            .filter(page => page && page.title && page.slug)
            .sort((a, b) => a.title.localeCompare(b.title));
          setCmsPages(validPages);
        } else {
          setCmsPages([]);
        }
      } catch (error) {
        // Silently handle errors to prevent footer from breaking
        console.error('Error fetching CMS pages for footer:', error);
        setCmsPages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCMSPages();
  }, []);

  return (
    <footer className="footer-component footer">
      <div className="footer-container max-container">
        <div className="footer-logo-section">
          <img
            src={getSiteLogoUrl() || logo}
            alt={siteName || "XO Sports Hub"}
            className="footer-logo"
          />
          <p className="footer-tagline">
            "Elevate Your Game - A Digital Exchange of Sports Strategies"
          </p>
          <div className="footer-contact">
             <div className="Contact__social-icons">
              <div className="footericonborder"><FaFacebookF  className="footer__social-icon" /></div>
              <div className="footericonborder"> <AiFillInstagram className="footer__social-icon" /></div>
              <div className="footericonborder"> <FaSquareXTwitter className="footer__social-icon" /></div>
            </div>
            <h3 className="mt-30">Get in Touch</h3>
            <p>
              <FaEnvelope size={18} /> {contactEmail || "<EMAIL>"}
            </p>
           
          </div>
        </div>

        <div className="footer-links">
          <h3>Quick Links</h3>
          <ul className="quickLinks">
            <li>
              <Link to="/">Home</Link>
            </li>

            <li>
              <Link to="/contact">Contact Us</Link>
            </li>

            {/* Loading state for CMS pages */}
            {loading && (
              <li>
                <span style={{ color: 'var(--secondary-color)', fontSize: 'var(--smallfont)' }}>
                  Loading pages...
                </span>
              </li>
            )}

            {/* Dynamically render published CMS pages (limit to 5 for footer) */}
            {!loading && cmsPages.length > 0 && cmsPages
              .slice(0, 5) // Limit to 5 pages to keep footer clean
              .map((page) => (
                <li key={page._id || page.id}>
                  <Link to={`/cms/${page.slug}`} title={page.metaDescription || page.title}>
                    {page.title}
                  </Link>
                </li>
              ))}

          </ul>
        </div>

        <div className="footer-sports">
          <h3>What Sport Do You Want To Learn?</h3>
          <div className="footer-sports-grid">
            <div className="footer-sports-column">
              <ul>

                <li>
                  <Link to="/">Baseball</Link>
                </li>
                <li>
                  <Link to="/">Basketball</Link>
                </li>
                <li>
                  <Link to="/">Football</Link>
                </li>
              </ul>
            </div>
            <div className="footer-sports-column">
              <ul>
                <li>
                  <Link to="/">Health & Fitness</Link>
                </li>
                <li>
                  <Link to="/">Hockey</Link>
                </li>
                <li>
                  <Link to="/">Mental Training</Link>
                </li>
              </ul>
            </div>
            <div className="footer-sports-column">
              <ul>
                <li>
                  <Link to="/">Soccer</Link>
                </li>
                <li>
                  <Link to="/">Youth coaching</Link>
                </li>
                <li>
                  <Link to="/">All sports</Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-bottom max-container">
        <p>
          &copy; {new Date().getFullYear()} Sports Playbook Strategy
          Marketplace. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default Footer;
